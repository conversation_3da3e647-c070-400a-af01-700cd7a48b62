-- Telegram <PERSON>t Database Schema
-- Run this in your Supabase SQL Editor

-- Bot configuration table
CREATE TABLE IF NOT EXISTS bot_config (
  id SERIAL PRIMARY KEY,
  key TEXT UNIQUE NOT NULL,
  value TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Processed messages to prevent duplicate processing
CREATE TABLE IF NOT EXISTS processed_messages (
  id SERIAL PRIMARY KEY,
  message_id BIGINT NOT NULL,
  chat_id BIGINT NOT NULL,
  user_id BIGINT NOT NULL,
  tweet_url TEXT NOT NULL,
  processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(message_id, chat_id)
);

-- Tweet summaries cache
CREATE TABLE IF NOT EXISTS tweet_summaries (
  id SERIAL PRIMARY KEY,
  tweet_id TEXT UNIQUE NOT NULL,
  tweet_text TEXT NOT NULL,
  context_summary TEXT NOT NULL,
  context_tweets JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() + INTERVAL '24 hours'
);

-- Error logs for debugging
CREATE TABLE IF NOT EXISTS error_logs (
  id SERIAL PRIMARY KEY,
  error_type TEXT NOT NULL,
  error_message TEXT NOT NULL,
  stack_trace TEXT,
  context JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_processed_messages_chat_id ON processed_messages(chat_id);
CREATE INDEX IF NOT EXISTS idx_processed_messages_user_id ON processed_messages(user_id);
CREATE INDEX IF NOT EXISTS idx_tweet_summaries_tweet_id ON tweet_summaries(tweet_id);
CREATE INDEX IF NOT EXISTS idx_tweet_summaries_expires_at ON tweet_summaries(expires_at);
CREATE INDEX IF NOT EXISTS idx_error_logs_created_at ON error_logs(created_at);

-- Insert initial configuration
INSERT INTO bot_config (key, value) VALUES 
  ('allowed_group_id', '-1001234567890'),
  ('allowed_topic_id', '123'),
  ('rate_limit_per_minute', '5'),
  ('summary_cache_hours', '24'),
  ('max_context_depth', '5')
ON CONFLICT (key) DO NOTHING;

-- Create a function to clean up expired summaries
CREATE OR REPLACE FUNCTION cleanup_expired_summaries()
RETURNS void AS $$
BEGIN
  DELETE FROM tweet_summaries WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- Create a function to log errors
CREATE OR REPLACE FUNCTION log_error(
  error_type_param TEXT,
  error_message_param TEXT,
  stack_trace_param TEXT DEFAULT NULL,
  context_param JSONB DEFAULT NULL
)
RETURNS void AS $$
BEGIN
  INSERT INTO error_logs (error_type, error_message, stack_trace, context)
  VALUES (error_type_param, error_message_param, stack_trace_param, context_param);
END;
$$ LANGUAGE plpgsql;