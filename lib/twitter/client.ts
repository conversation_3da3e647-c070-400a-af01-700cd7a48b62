import { createClient } from "@/lib/supabase/server";

export interface Tweet {
  id: string;
  text: string;
  author: {
    id: string;
    username: string;
    name: string;
  };
  created_at: string;
  public_metrics: {
    retweet_count: number;
    like_count: number;
    reply_count: number;
    quote_count: number;
  };
  referenced_tweets?: Array<{
    type: 'replied_to' | 'quoted' | 'retweeted';
    id: string;
  }>;
  in_reply_to_user_id?: string;
}

interface RawTweet {
  id: string;
  text: string;
  author_id: string;
  created_at: string;
  public_metrics: {
    retweet_count: number;
    like_count: number;
    reply_count: number;
    quote_count: number;
  };
  referenced_tweets?: Array<{
    type: 'replied_to' | 'quoted' | 'retweeted';
    id: string;
  }>;
  in_reply_to_user_id?: string;
}

export interface TwitterApiResponse {
  data: RawTweet[];
  includes?: {
    users?: Array<{
      id: string;
      username: string;
      name: string;
    }>;
    tweets?: RawTweet[];
  };
}

class TwitterApiError extends <PERSON>rror {
  constructor(message: string, public status?: number) {
    super(message);
    this.name = 'TwitterApiError';
  }
}

export class TwitterClient {
  private apiKey: string;
  private baseUrl = 'https://api.twitter.com/2';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async fetchTweets(tweetIds: string[]): Promise<Tweet[]> {
    const params = new URLSearchParams({
      ids: tweetIds.join(','),
      'tweet.fields': 'id,text,author_id,created_at,public_metrics,referenced_tweets,in_reply_to_user_id',
      'user.fields': 'id,username,name',
      'expansions': 'author_id,referenced_tweets.id,in_reply_to_user_id'
    });

    const response = await fetch(`${this.baseUrl}/tweets?${params}`, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new TwitterApiError(`Twitter API error: ${response.status}`, response.status);
    }

    const data: TwitterApiResponse = await response.json();
    
    if (!data.data) {
      throw new TwitterApiError('No tweet data returned');
    }

    // Merge user data with tweets
    const tweets = data.data.map(tweet => ({
      ...tweet,
      author: data.includes?.users?.find(user => user.id === tweet.author_id) || {
        id: tweet.author_id,
        username: 'unknown',
        name: 'Unknown User'
      }
    }));

    return tweets;
  }

  async fetchTweetContext(tweetId: string, depth = 5): Promise<Tweet[]> {
    const context: Tweet[] = [];
    const processedIds = new Set<string>();
    let currentId = tweetId;
    let currentDepth = 0;

    while (currentId && currentDepth < depth && !processedIds.has(currentId)) {
      try {
        const [tweet] = await this.fetchTweets([currentId]);
        if (!tweet) break;

        context.push(tweet);
        processedIds.add(currentId);

        // Add referenced tweets (quotes, retweets)
        if (tweet.referenced_tweets) {
          for (const ref of tweet.referenced_tweets) {
            if (!processedIds.has(ref.id)) {
              try {
                const [refTweet] = await this.fetchTweets([ref.id]);
                if (refTweet) {
                  context.push(refTweet);
                  processedIds.add(ref.id);
                }
              } catch (error) {
                console.warn(`Failed to fetch referenced tweet ${ref.id}:`, error);
              }
            }
          }
        }

        // Follow reply chain
        const replyRef = tweet.referenced_tweets?.find(ref => ref.type === 'replied_to');
        currentId = replyRef?.id || '';
        currentDepth++;
        
      } catch (error) {
        console.warn(`Failed to fetch tweet ${currentId}:`, error);
        break;
      }
    }

    return context;
  }

  async getCachedSummary(tweetId: string): Promise<{ text: string; summary: string; context: Tweet[] } | null> {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from('BuddyIntels_tweet_summaries')
      .select('*')
      .eq('tweet_id', tweetId)
      .gt('expires_at', new Date().toISOString())
      .single();

    if (error || !data) return null;

    return {
      text: data.tweet_text,
      summary: data.context_summary,
      context: data.context_tweets
    };
  }

  async cacheSummary(tweetId: string, tweetText: string, summary: string, context: Tweet[]): Promise<void> {
    const supabase = await createClient();
    
    await supabase
      .from('BuddyIntels_tweet_summaries')
      .upsert({
        tweet_id: tweetId,
        tweet_text: tweetText,
        context_summary: summary,
        context_tweets: context,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      });
  }
}

export const twitterClient = new TwitterClient(process.env.TWITTER_API_KEY!);