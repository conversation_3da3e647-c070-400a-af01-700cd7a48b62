import { Bot, Context, NextFunction } from 'grammy';
import { twitterClient, extractTweetUrls, extractTweetId, Tweet } from '@/lib/twitter';
import { aiSummarizer } from '@/lib/ai';
import { configManager } from './config';
import { rateLimiter } from './rate-limiter';
import { createClient } from '@/lib/supabase/server';

export class TelegramBot {
  private bot: Bot;
  private processingQueue = new Map<string, Promise<void>>();

  constructor(token: string) {
    this.bot = new Bot(token);
    this.setupMiddleware();
    this.setupHandlers();
  }

  private setupMiddleware() {
    // Error handling middleware
    this.bot.catch(async (err) => {
      console.error('Bot error:', err);
      await this.logError('bot_error', err.message, err.stack, {
        update: err.ctx?.update,
        chat_id: err.ctx?.chat?.id,
        user_id: err.ctx?.from?.id
      });
    });

    // Rate limiting middleware
    this.bot.use(async (ctx: Context, next: NextFunction) => {
      if (!ctx.from) return;

      const config = await configManager.getBotConfig();
      const canProceed = await rateLimiter.checkRateLimit(
        ctx.from.id.toString(),
        config.rateLimitPerMinute
      );

      if (!canProceed) {
        const resetTime = await rateLimiter.getResetTime(ctx.from.id.toString());
        await ctx.reply(
          `⏰ Rate limit exceeded. Please wait ${Math.ceil(resetTime / 1000)} seconds before sending another request.`,
          { reply_to_message_id: ctx.message?.message_id }
        );
        return;
      }

      await next();
    });
  }

  private setupHandlers() {
    // Start command
    this.bot.command('start', async (ctx) => {
      await ctx.reply(
        '🤖 Hello! I\'m a Twitter context summarizer bot.\n\n' +
        'Send me a message containing a Twitter/X URL and I\'ll provide a summary of the tweet\'s context and thread.\n\n' +
        'Commands:\n' +
        '/help - Show this help message\n' +
        '/status - Check bot status'
      );
    });

    // Help command
    this.bot.command('help', async (ctx) => {
      await ctx.reply(
        '📝 **How to use this bot:**\n\n' +
        '1. Send a message containing a Twitter/X URL\n' +
        '2. I\'ll analyze the tweet and its context\n' +
        '3. You\'ll receive a summary with key points\n\n' +
        '**Supported URLs:**\n' +
        '• twitter.com/username/status/123\n' +
        '• x.com/username/status/123\n\n' +
        '**Features:**\n' +
        '• Thread analysis\n' +
        '• Context summarization\n' +
        '• Sentiment analysis\n' +
        '• Participant identification\n\n' +
        '**Rate Limits:**\n' +
        '• 5 requests per minute per user\n' +
        '• Summaries are cached for 24 hours',
        { parse_mode: 'Markdown' }
      );
    });

    // Status command
    this.bot.command('status', async (ctx) => {
      try {
        const config = await configManager.getBotConfig();
        const remaining = await rateLimiter.getRemainingRequests(
          ctx.from!.id.toString(),
          config.rateLimitPerMinute
        );

        await ctx.reply(
          `🟢 **Bot Status: Online**\n\n` +
          `📊 **Your Rate Limit:**\n` +
          `• Remaining requests: ${remaining}/${config.rateLimitPerMinute}\n` +
          `• Reset time: ${config.rateLimitPerMinute} per minute\n\n` +
          `⚙️ **Configuration:**\n` +
          `• Cache duration: ${config.summaryCacheHours}h\n` +
          `• Max context depth: ${config.maxContextDepth}\n\n` +
          `💾 **Database:** Connected`,
          { parse_mode: 'Markdown' }
        );
      } catch {
        await ctx.reply('❌ Error checking status. Please try again.');
      }
    });

    // Message handler for tweet URLs
    this.bot.on('message:text', async (ctx) => {
      await this.handleTweetMessage(ctx);
    });
  }

  private async handleTweetMessage(ctx: Context) {
    const message = ctx.message;
    if (!message?.text || !ctx.from) return;

    // Check if message is in allowed group/topic
    const config = await configManager.getBotConfig();
    const isAllowedGroup = config.allowedGroupId === '' || 
                          message.chat.id.toString() === config.allowedGroupId;
    const isAllowedTopic = config.allowedTopicId === '' || 
                          message.message_thread_id?.toString() === config.allowedTopicId;

    if (!isAllowedGroup || !isAllowedTopic) {
      return; // Silently ignore messages from unauthorized groups/topics
    }

    // Extract tweet URLs
    const tweetUrls = extractTweetUrls(message.text);
    if (tweetUrls.length === 0) return;

    // Process first tweet URL found
    const tweetUrl = tweetUrls[0];
    const tweetId = extractTweetId(tweetUrl);
    if (!tweetId) return;

    // Check if already processing this message
    const messageKey = `${message.chat.id}:${message.message_id}`;
    if (this.processingQueue.has(messageKey)) {
      return;
    }

    // Check if message was already processed
    const alreadyProcessed = await this.isMessageProcessed(
      message.message_id,
      message.chat.id
    );
    if (alreadyProcessed) {
      return;
    }

    // Add to processing queue
    const processingPromise = this.processTweetUrl(ctx, tweetId, tweetUrl);
    this.processingQueue.set(messageKey, processingPromise);

    try {
      await processingPromise;
    } finally {
      this.processingQueue.delete(messageKey);
    }
  }

  private async processTweetUrl(ctx: Context, tweetId: string, tweetUrl: string) {
    const message = ctx.message!;
    const user = ctx.from!;
    
    // Send "typing" indicator
    await ctx.replyWithChatAction('typing');

    try {
      // Check cache first
      const cached = await twitterClient.getCachedSummary(tweetId);
      if (cached) {
        const replyText = await aiSummarizer.generateReplyText(
          { id: tweetId, text: cached.text, author: { username: 'cached', name: 'Cached', id: 'cached' } } as Tweet,
          { summary: cached.summary, key_points: [], sentiment: 'neutral', participants: [] }
        );
        
        await ctx.reply(replyText, {
          reply_to_message_id: message.message_id,
          parse_mode: 'Markdown'
        });
        return;
      }

      // Fetch tweet context
      const config = await configManager.getBotConfig();
      const tweets = await twitterClient.fetchTweetContext(tweetId, config.maxContextDepth);
      
      if (tweets.length === 0) {
        await ctx.reply(
          '❌ Sorry, I couldn\'t fetch this tweet. It might be private, deleted, or there was an API error.',
          { reply_to_message_id: message.message_id }
        );
        return;
      }

      // Get the original tweet
      const originalTweet = tweets.find(t => t.id === tweetId) || tweets[0];

      // Generate summary
      const summary = await aiSummarizer.summarizeContext(tweets);
      
      // Cache the summary
      await twitterClient.cacheSummary(tweetId, originalTweet.text, summary.summary, tweets);

      // Generate and send reply
      const replyText = await aiSummarizer.generateReplyText(originalTweet, summary);
      await ctx.reply(replyText, {
        reply_to_message_id: message.message_id,
        parse_mode: 'Markdown'
      });

      // Mark message as processed
      await this.markMessageProcessed(message.message_id, message.chat.id, user.id, tweetUrl);

    } catch (error) {
      console.error('Error processing tweet:', error);
      await this.logError('tweet_processing_error', 
        error instanceof Error ? error.message : 'Unknown error', 
        error instanceof Error ? error.stack : undefined, 
        {
          tweet_id: tweetId,
          tweet_url: tweetUrl,
          user_id: user.id,
          chat_id: message.chat.id
        });

      await ctx.reply(
        '❌ Sorry, I encountered an error while processing this tweet. Please try again later.',
        { reply_to_message_id: message.message_id }
      );
    }
  }

  private async isMessageProcessed(messageId: number, chatId: number): Promise<boolean> {
    try {
      const supabase = await createClient();
      const { data, error } = await supabase
        .from('BuddyIntels_processed_messages')
        .select('id')
        .eq('message_id', messageId)
        .eq('chat_id', chatId)
        .single();

      return !error && data !== null;
    } catch (error) {
      console.error('Error checking processed message:', error);
      return false;
    }
  }

  private async markMessageProcessed(messageId: number, chatId: number, userId: number, tweetUrl: string): Promise<void> {
    try {
      const supabase = await createClient();
      await supabase
        .from('BuddyIntels_processed_messages')
        .insert({
          message_id: messageId,
          chat_id: chatId,
          user_id: userId,
          tweet_url: tweetUrl
        });
    } catch (error) {
      console.error('Error marking message as processed:', error);
    }
  }

  private async logError(errorType: string, message: string, stackTrace?: string, context?: unknown): Promise<void> {
    try {
      const supabase = await createClient();
      await supabase
        .from('BuddyIntels_error_logs')
        .insert({
          error_type: errorType,
          error_message: message,
          stack_trace: stackTrace,
          context: context || {}
        });
    } catch (error) {
      console.error('Error logging to database:', error);
    }
  }

  getBot(): Bot {
    return this.bot;
  }
}

export function createTelegramBot(): TelegramBot {
  const token = process.env.TELEGRAM_BOT_TOKEN;
  if (!token) {
    throw new Error('TELEGRAM_BOT_TOKEN environment variable is required');
  }
  
  return new TelegramBot(token);
}