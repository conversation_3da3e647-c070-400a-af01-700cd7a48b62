import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// GET /api/topics/[id] - Get a specific topic
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const topicId = parseInt(params.id);
  console.log(`[API] GET /api/topics/${topicId} - Fetching specific topic`);
  
  try {
    if (isNaN(topicId)) {
      return NextResponse.json(
        { error: 'Invalid topic ID' },
        { status: 400 }
      );
    }

    const supabase = await createClient();
    
    const { data: topic, error } = await supabase
      .from('BuddyIntels_linked_topics')
      .select(`
        *,
        BuddyIntels_topic_activity_logs(
          id,
          user_id,
          username,
          action,
          details,
          created_at
        )
      `)
      .eq('id', topicId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Topic not found' },
          { status: 404 }
        );
      }
      
      console.error(`[API] Error fetching topic ${topicId}:`, error);
      return NextResponse.json(
        { error: 'Failed to fetch topic' },
        { status: 500 }
      );
    }

    console.log(`[API] Successfully fetched topic ${topicId}`);
    
    return NextResponse.json({
      success: true,
      data: topic
    });

  } catch (error) {
    console.error(`[API] Unexpected error in GET /api/topics/${topicId}:`, error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PATCH /api/topics/[id] - Update a specific topic
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const topicId = parseInt(params.id);
  console.log(`[API] PATCH /api/topics/${topicId} - Updating specific topic`);
  
  try {
    if (isNaN(topicId)) {
      return NextResponse.json(
        { error: 'Invalid topic ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { 
      is_active, 
      group_title, 
      topic_title, 
      user_id, 
      username,
      action_note 
    } = body;

    if (user_id === undefined) {
      return NextResponse.json(
        { error: 'user_id is required for tracking changes' },
        { status: 400 }
      );
    }

    const supabase = await createClient();

    // Check if topic exists
    const { data: existingTopic, error: fetchError } = await supabase
      .from('BuddyIntels_linked_topics')
      .select('*')
      .eq('id', topicId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Topic not found' },
          { status: 404 }
        );
      }
      
      console.error(`[API] Error fetching topic ${topicId}:`, fetchError);
      return NextResponse.json(
        { error: 'Failed to fetch topic' },
        { status: 500 }
      );
    }

    // Prepare update data
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (is_active !== undefined) {
      updateData.is_active = is_active;
    }
    if (group_title !== undefined) {
      updateData.group_title = group_title;
    }
    if (topic_title !== undefined) {
      updateData.topic_title = topic_title;
    }

    // Update the topic
    const { data: updatedTopic, error: updateError } = await supabase
      .from('BuddyIntels_linked_topics')
      .update(updateData)
      .eq('id', topicId)
      .select()
      .single();

    if (updateError) {
      console.error(`[API] Error updating topic ${topicId}:`, updateError);
      return NextResponse.json(
        { error: 'Failed to update topic' },
        { status: 500 }
      );
    }

    // Log the activity
    let action = 'updated';
    if (is_active !== undefined) {
      action = is_active ? 'activated' : 'deactivated';
    }

    await supabase
      .from('BuddyIntels_topic_activity_logs')
      .insert({
        topic_id: topicId,
        user_id: user_id,
        username: username,
        action: action,
        details: {
          changes: updateData,
          note: action_note
        }
      });

    console.log(`[API] Successfully updated topic ${topicId}`);
    
    return NextResponse.json({
      success: true,
      message: 'Topic updated successfully',
      data: updatedTopic
    });

  } catch (error) {
    console.error(`[API] Unexpected error in PATCH /api/topics/${topicId}:`, error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/topics/[id] - Delete a specific topic
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const topicId = parseInt(params.id);
  console.log(`[API] DELETE /api/topics/${topicId} - Deleting specific topic`);
  
  try {
    if (isNaN(topicId)) {
      return NextResponse.json(
        { error: 'Invalid topic ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { user_id, username, reason } = body;

    if (!user_id) {
      return NextResponse.json(
        { error: 'user_id is required for tracking deletions' },
        { status: 400 }
      );
    }

    const supabase = await createClient();

    // Check if topic exists
    const { data: existingTopic, error: fetchError } = await supabase
      .from('BuddyIntels_linked_topics')
      .select('*')
      .eq('id', topicId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Topic not found' },
          { status: 404 }
        );
      }
      
      console.error(`[API] Error fetching topic ${topicId}:`, fetchError);
      return NextResponse.json(
        { error: 'Failed to fetch topic' },
        { status: 500 }
      );
    }

    // Log the deletion before actually deleting
    await supabase
      .from('BuddyIntels_topic_activity_logs')
      .insert({
        topic_id: topicId,
        user_id: user_id,
        username: username,
        action: 'deleted',
        details: {
          topic_data: existingTopic,
          reason: reason
        }
      });

    // Delete the topic
    const { error: deleteError } = await supabase
      .from('BuddyIntels_linked_topics')
      .delete()
      .eq('id', topicId);

    if (deleteError) {
      console.error(`[API] Error deleting topic ${topicId}:`, deleteError);
      return NextResponse.json(
        { error: 'Failed to delete topic' },
        { status: 500 }
      );
    }

    console.log(`[API] Successfully deleted topic ${topicId}`);
    
    return NextResponse.json({
      success: true,
      message: 'Topic deleted successfully'
    });

  } catch (error) {
    console.error(`[API] Unexpected error in DELETE /api/topics/${topicId}:`, error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
