import { createClient } from "@/lib/supabase/server";

export interface BotConfig {
  allowedGroupId: string;
  allowedTopicId: string;
  rateLimitPerMinute: number;
  summaryCacheHours: number;
  maxContextDepth: number;
}

export class ConfigManager {
  private cache = new Map<string, string>();
  private lastFetch = 0;
  private cacheTtl = 5 * 60 * 1000; // 5 minutes

  async getConfig(key: string): Promise<string | null> {
    // Check cache first
    if (this.cache.has(key) && Date.now() - this.lastFetch < this.cacheTtl) {
      return this.cache.get(key) || null;
    }

    try {
      const supabase = await createClient();
      const { data, error } = await supabase
        .from('BuddyIntels_bot_config')
        .select('value')
        .eq('key', key)
        .single();

      if (error) {
        console.error(`Failed to get config ${key}:`, error);
        return null;
      }

      this.cache.set(key, data.value);
      this.lastFetch = Date.now();
      return data.value;
    } catch (error) {
      console.error(`Error fetching config ${key}:`, error);
      return null;
    }
  }

  async getBotConfig(): Promise<BotConfig> {
    const [allowedGroupId, allowedTopicId, rateLimitPerMinute, summaryCacheHours, maxContextDepth] = await Promise.all([
      this.getConfig('allowed_group_id'),
      this.getConfig('allowed_topic_id'),
      this.getConfig('rate_limit_per_minute'),
      this.getConfig('summary_cache_hours'),
      this.getConfig('max_context_depth')
    ]);

    return {
      allowedGroupId: allowedGroupId || process.env.TELEGRAM_GROUP_ID || '',
      allowedTopicId: allowedTopicId || process.env.TELEGRAM_TOPIC_ID || '',
      rateLimitPerMinute: parseInt(rateLimitPerMinute || '5'),
      summaryCacheHours: parseInt(summaryCacheHours || '24'),
      maxContextDepth: parseInt(maxContextDepth || '5')
    };
  }

  async updateConfig(key: string, value: string): Promise<void> {
    try {
      const supabase = await createClient();
      await supabase
        .from('BuddyIntels_bot_config')
        .upsert({ key, value });
      
      // Update cache
      this.cache.set(key, value);
    } catch (error) {
      console.error(`Error updating config ${key}:`, error);
      throw error;
    }
  }

  clearCache(): void {
    this.cache.clear();
    this.lastFetch = 0;
  }
}

export const configManager = new ConfigManager();