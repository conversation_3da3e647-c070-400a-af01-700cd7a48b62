import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    // Check database connection
    const supabase = await createClient();
    const { error } = await supabase
      .from('BuddyIntels_bot_config')
      .select('count')
      .limit(1)
      .single();

    const dbStatus = error ? 'error' : 'connected';
    
    // Check environment variables
    const envChecks = {
      telegram_bot_token: !!process.env.TELEGRAM_BOT_TOKEN,
      openrouter_api_key: !!process.env.OPENROUTER_API_KEY,
      twitter_api_key: !!process.env.TWITTER_API_KEY,
      supabase_url: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      supabase_key: !!process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY
    };

    const allEnvVarsPresent = Object.values(envChecks).every(Boolean);

    return NextResponse.json({
      status: dbStatus === 'connected' && allEnvVarsPresent ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      checks: {
        database: dbStatus,
        environment: envChecks
      }
    });

  } catch (error) {
    console.error('Health check error:', error);
    return NextResponse.json(
      {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}