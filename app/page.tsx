import { createClient } from "@/lib/supabase/server";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

export default async function Home() {
  const supabase = await createClient();
  
  // Simple health check
  const { data: healthCheck } = await supabase.from('BuddyIntels_bot_config').select('count').limit(1).maybeSingle();
  
  return (
    <main className="min-h-screen flex flex-col items-center justify-center p-4">
      <div className="text-center space-y-6">
        <h1 className="text-4xl font-bold text-foreground">
          Telegram Bot
        </h1>
        <p className="text-muted-foreground max-w-md">
          Tweet Context Summarization Bot for Telegram
        </p>
        <div className="mt-8 p-4 bg-muted rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Bot Status</h2>
          <p className="text-sm text-muted-foreground">
            Database: {healthCheck !== undefined ? "✅ Connected" : "❌ Error"}
          </p>
          <p className="text-sm text-muted-foreground">
            Webhook: Ready to receive messages
          </p>
        </div>

        <div className="mt-6">
          <Link href="/dashboard">
            <Button size="lg">
              Open Topic Management Dashboard
            </Button>
          </Link>
        </div>
      </div>
    </main>
  );
}