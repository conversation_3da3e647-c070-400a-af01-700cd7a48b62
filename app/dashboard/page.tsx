import { Metadata } from "next";
import { createClient } from "@/lib/supabase/server";
import { TopicDashboard } from "@/components/dashboard/topic-dashboard";

export const metadata: Metadata = {
  title: "Topic Management Dashboard",
  description: "Manage Telegram topics for Twitter URL monitoring",
};

export default async function DashboardPage() {
  const supabase = await createClient();
  
  // Fetch active topics
  const { data: topics } = await supabase
    .from('BuddyIntels_linked_topics')
    .select('*')
    .order('created_at', { ascending: false });
  
  // Fetch recent activity
  const { data: recentActivity } = await supabase
    .from('BuddyIntels_topic_activity_logs')
    .select(`
      *,
      BuddyIntels_linked_topics(
        id,
        group_title,
        topic_title
      )
    `)
    .order('created_at', { ascending: false })
    .limit(10);
  
  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-8">Topic Management Dashboard</h1>
      
      <TopicDashboard 
        initialTopics={topics || []} 
        recentActivity={recentActivity || []} 
      />
    </div>
  );
}
