import { generateText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { Tweet } from '@/lib/twitter';

export type SummaryResult = {
  summary: string;
  key_points: string[];
  sentiment: 'positive' | 'negative' | 'neutral';
  participants: string[];
};

export class AISummarizer {
  private model;

  constructor() {
    // Use OpenAI client
    this.model = openai('gpt-4o-mini');
  }

  async summarizeContext(tweets: Tweet[]): Promise<SummaryResult> {
    if (tweets.length === 0) {
      throw new Error('No tweets provided for summarization');
    }

    // Sort tweets by creation date to maintain chronological order
    const sortedTweets = [...tweets].sort((a, b) => 
      new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
    );

    // Format tweets for the AI prompt
    const tweetContext = sortedTweets.map((tweet, index) => {
      const metrics = tweet.public_metrics;
      return `${index + 1}. @${tweet.author.username} (${tweet.author.name}):
"${tweet.text}"
[${metrics.like_count} likes, ${metrics.retweet_count} retweets, ${metrics.reply_count} replies]
Posted: ${new Date(tweet.created_at).toLocaleString()}`;
    }).join('\n\n');

    const prompt = `You are analyzing a Twitter/X thread or conversation context. Please provide a concise summary of the following tweets and their context:

${tweetContext}

Please analyze this context and provide:
1. A clear, concise summary of what this thread/conversation is about
2. Key points or insights from the discussion
3. Overall sentiment (positive, negative, or neutral)
4. Key participants or accounts involved

Focus on the main narrative, important points, and context that would help someone understand what this conversation is about without reading all the tweets.`;

    try {
      const result = await generateText({
        model: this.model,
        prompt,
        maxTokens: 500,
        temperature: 0.3,
      });

      // Parse the result - since we're not using structured output, we'll extract info manually
      const summary = result.text;
      
      // Extract key information from the summary
      const keyPoints = this.extractKeyPoints(summary);
      const sentiment = this.detectSentiment(summary);
      const participants = this.extractParticipants(tweets);

      return {
        summary: summary.trim(),
        key_points: keyPoints,
        sentiment,
        participants
      };
    } catch (error) {
      console.error('AI summarization error:', error);
      throw new Error('Failed to generate summary');
    }
  }

  private extractKeyPoints(text: string): string[] {
    // Simple extraction of bullet points or numbered lists
    const lines = text.split('\n');
    const keyPoints: string[] = [];
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.match(/^[\-\*\d\.]/)) {
        keyPoints.push(trimmed.replace(/^[\-\*\d\.\s]+/, ''));
      }
    }
    
    return keyPoints.slice(0, 5); // Limit to top 5 points
  }

  private detectSentiment(text: string): 'positive' | 'negative' | 'neutral' {
    const positiveWords = ['good', 'great', 'excellent', 'positive', 'success', 'happy', 'love', 'amazing'];
    const negativeWords = ['bad', 'terrible', 'negative', 'failure', 'sad', 'hate', 'awful', 'worse'];
    
    const lowerText = text.toLowerCase();
    const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  private extractParticipants(tweets: Tweet[]): string[] {
    const participants = new Set<string>();
    
    for (const tweet of tweets) {
      participants.add(`@${tweet.author.username}`);
    }
    
    return Array.from(participants).slice(0, 5); // Limit to top 5 participants
  }

  async generateReplyText(originalTweet: Tweet, summary: SummaryResult): Promise<string> {
    const replyText = `🧵 **Tweet Context Summary**

**Original Tweet:**
${originalTweet.text}

**Context Summary:**
${summary.summary}

${summary.key_points.length > 0 ? `**Key Points:**
${summary.key_points.map(point => `• ${point}`).join('\n')}` : ''}

**Participants:** ${summary.participants.join(', ')}
**Sentiment:** ${summary.sentiment === 'positive' ? '😊' : summary.sentiment === 'negative' ? '😔' : '😐'} ${summary.sentiment}

_Generated by AI • Context may be incomplete_`;

    return replyText;
  }
}

export const aiSummarizer = new AISummarizer();