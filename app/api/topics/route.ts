import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { topicManager } from '@/lib/telegram/topic-manager';

// GET /api/topics - List all linked topics
export async function GET(request: NextRequest) {
  console.log('[API] GET /api/topics - Fetching all linked topics');
  
  try {
    const { searchParams } = new URL(request.url);
    const activeOnly = searchParams.get('active') === 'true';
    const userId = searchParams.get('userId');

    const supabase = await createClient();
    
    let query = supabase
      .from('BuddyIntels_linked_topics')
      .select(`
        *,
        BuddyIntels_topic_activity_logs!inner(
          action,
          created_at
        )
      `)
      .order('created_at', { ascending: false });

    if (activeOnly) {
      query = query.eq('is_active', true);
    }

    if (userId) {
      query = query.eq('linked_by_user_id', parseInt(userId));
    }

    const { data: topics, error } = await query;

    if (error) {
      console.error('[API] Error fetching topics:', error);
      return NextResponse.json(
        { error: 'Failed to fetch topics' },
        { status: 500 }
      );
    }

    console.log(`[API] Successfully fetched ${topics?.length || 0} topics`);
    
    return NextResponse.json({
      success: true,
      data: topics || [],
      count: topics?.length || 0
    });

  } catch (error) {
    console.error('[API] Unexpected error in GET /api/topics:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/topics - Create a new linked topic (for dashboard use)
export async function POST(request: NextRequest) {
  console.log('[API] POST /api/topics - Creating new linked topic');
  
  try {
    const body = await request.json();
    const {
      group_id,
      topic_id,
      group_title,
      topic_title,
      linked_by_user_id,
      linked_by_username,
      linked_by_first_name
    } = body;

    // Validate required fields
    if (!group_id || !linked_by_user_id) {
      return NextResponse.json(
        { error: 'group_id and linked_by_user_id are required' },
        { status: 400 }
      );
    }

    const result = await topicManager.linkTopic(
      group_id,
      topic_id || null,
      group_title || null,
      topic_title || null,
      linked_by_user_id,
      linked_by_username || null,
      linked_by_first_name || null
    );

    if (result.success) {
      console.log('[API] Successfully created linked topic');
      return NextResponse.json({
        success: true,
        message: result.message,
        data: result.topicData
      });
    } else {
      console.log('[API] Failed to create linked topic:', result.message);
      return NextResponse.json(
        { error: result.message },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('[API] Unexpected error in POST /api/topics:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PATCH /api/topics - Update multiple topics (bulk operations)
export async function PATCH(request: NextRequest) {
  console.log('[API] PATCH /api/topics - Bulk update topics');
  
  try {
    const body = await request.json();
    const { action, topic_ids, user_id, username } = body;

    if (!action || !topic_ids || !Array.isArray(topic_ids) || !user_id) {
      return NextResponse.json(
        { error: 'action, topic_ids (array), and user_id are required' },
        { status: 400 }
      );
    }

    const supabase = await createClient();
    const results = [];

    for (const topicId of topic_ids) {
      try {
        if (action === 'activate') {
          const { error } = await supabase
            .from('BuddyIntels_linked_topics')
            .update({ 
              is_active: true, 
              updated_at: new Date().toISOString() 
            })
            .eq('id', topicId);

          if (!error) {
            // Log activity
            await supabase
              .from('BuddyIntels_topic_activity_logs')
              .insert({
                topic_id: topicId,
                user_id: user_id,
                username: username,
                action: 'activated'
              });
          }

          results.push({ topicId, success: !error, error: error?.message });

        } else if (action === 'deactivate') {
          const { error } = await supabase
            .from('BuddyIntels_linked_topics')
            .update({ 
              is_active: false, 
              updated_at: new Date().toISOString() 
            })
            .eq('id', topicId);

          if (!error) {
            // Log activity
            await supabase
              .from('BuddyIntels_topic_activity_logs')
              .insert({
                topic_id: topicId,
                user_id: user_id,
                username: username,
                action: 'deactivated'
              });
          }

          results.push({ topicId, success: !error, error: error?.message });

        } else {
          results.push({ 
            topicId, 
            success: false, 
            error: `Unknown action: ${action}` 
          });
        }
      } catch (error) {
        results.push({ 
          topicId, 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    console.log(`[API] Bulk update completed: ${successCount}/${results.length} successful`);

    return NextResponse.json({
      success: true,
      message: `${successCount}/${results.length} topics updated successfully`,
      results
    });

  } catch (error) {
    console.error('[API] Unexpected error in PATCH /api/topics:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/topics - Delete topics (bulk operation)
export async function DELETE(request: NextRequest) {
  console.log('[API] DELETE /api/topics - Bulk delete topics');
  
  try {
    const body = await request.json();
    const { topic_ids, user_id, username } = body;

    if (!topic_ids || !Array.isArray(topic_ids) || !user_id) {
      return NextResponse.json(
        { error: 'topic_ids (array) and user_id are required' },
        { status: 400 }
      );
    }

    const supabase = await createClient();
    const results = [];

    for (const topicId of topic_ids) {
      try {
        // Log activity before deletion
        await supabase
          .from('BuddyIntels_topic_activity_logs')
          .insert({
            topic_id: topicId,
            user_id: user_id,
            username: username,
            action: 'deleted'
          });

        // Delete the topic
        const { error } = await supabase
          .from('BuddyIntels_linked_topics')
          .delete()
          .eq('id', topicId);

        results.push({ topicId, success: !error, error: error?.message });

      } catch (error) {
        results.push({ 
          topicId, 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    console.log(`[API] Bulk delete completed: ${successCount}/${results.length} successful`);

    return NextResponse.json({
      success: true,
      message: `${successCount}/${results.length} topics deleted successfully`,
      results
    });

  } catch (error) {
    console.error('[API] Unexpected error in DELETE /api/topics:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
