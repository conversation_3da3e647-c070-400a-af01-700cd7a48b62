# Telegram Bot - Tweet Context Summarizer

A Telegram bot that analyzes Twitter/X URLs and provides AI-powered context summaries of tweets and their threads.

## Features

- **Tweet Context Analysis**: Fetches tweet threads, replies, quotes, and retweets
- **AI Summarization**: Uses Gemini 2.0 Flash via OpenRouter to generate concise summaries
- **Rate Limiting**: 5 requests per minute per user
- **Caching**: 24-hour cache for tweet summaries
- **Group/Topic Filtering**: Only responds in configured groups/topics
- **Error Handling**: Comprehensive error logging and user feedback

## Setup

### 1. Clone and Install

```bash
git clone <repository-url>
cd buddyintels
bun install
```

### 2. Database Setup

1. Create a Supabase project at [supabase.com](https://supabase.com)
2. Run the SQL schema in `supabase/schema.sql` in your Supabase SQL Editor
3. Note your project URL and anon key

### 3. Environment Variables

Copy `.env.example` to `.env.local` and configure:

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY=your-supabase-anon-key

# Telegram Bot (create via @BotFather)
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_GROUP_ID=-1001234567890  # Optional: restrict to specific group
TELEGRAM_TOPIC_ID=123             # Optional: restrict to specific topic

# OpenRouter (sign up at openrouter.ai)
OPENROUTER_API_KEY=your-openrouter-api-key

# Twitter API (get from Twitter Developer Portal)
TWITTER_API_KEY=your-twitter-api-bearer-token
```

### 4. Development

```bash
bun dev
```

### 5. Production Deployment

#### Deploy to Vercel

1. Connect your repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy

#### Set up Telegram Webhook

Once deployed, set the webhook:

```bash
curl -X POST "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/setWebhook" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://your-app.vercel.app/api/telegram/webhook"}'
```

## Usage

1. Add the bot to your Telegram group
2. Send a message containing a Twitter/X URL
3. The bot will reply with a context summary

### Commands

- `/start` - Welcome message
- `/help` - Usage instructions
- `/status` - Check bot status and rate limits

## Architecture

```
lib/
├── telegram/          # Telegram bot logic
│   ├── bot.ts        # Main bot handlers
│   ├── config.ts     # Configuration management
│   └── rate-limiter.ts # Rate limiting
├── twitter/          # Twitter API integration
│   ├── client.ts     # Twitter API client
│   └── parser.ts     # URL parsing utilities
├── ai/               # AI summarization
│   └── summarizer.ts # OpenRouter/Gemini integration
└── supabase/         # Database clients
```

## Configuration

Bot configuration is stored in the `bot_config` table:

| Key | Default | Description |
|-----|---------|-------------|
| `allowed_group_id` | `""` | Telegram group ID (empty = all groups) |
| `allowed_topic_id` | `""` | Topic ID within group (empty = all topics) |
| `rate_limit_per_minute` | `5` | Requests per minute per user |
| `summary_cache_hours` | `24` | Cache duration for summaries |
| `max_context_depth` | `5` | Maximum thread depth to analyze |

## API Endpoints

- `POST /api/telegram/webhook` - Telegram webhook handler
- `GET /api/telegram/webhook` - Bot health check
- `GET /api/health` - Application health check

## Error Handling

- All errors are logged to the `error_logs` table
- Users receive friendly error messages
- Rate limiting prevents abuse
- Duplicate message processing is prevented

## Development

### Database Schema

See `supabase/schema.sql` for the complete database schema including:
- `bot_config` - Configuration storage
- `processed_messages` - Duplicate prevention
- `tweet_summaries` - Summary caching
- `error_logs` - Error tracking

### Adding Features

1. Extend the bot handlers in `lib/telegram/bot.ts`
2. Add new configuration options in `lib/telegram/config.ts`
3. Update the database schema if needed
4. Add appropriate error handling

## Troubleshooting

1. **Bot not responding**: Check webhook setup and environment variables
2. **Rate limit errors**: Adjust `rate_limit_per_minute` in bot_config
3. **Twitter API errors**: Verify API key and rate limits
4. **Database errors**: Check Supabase connection and table schema

## License

MIT